import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/providers/language_provider.dart';
import 'package:achawach/providers/settings_provider.dart';
import 'package:achawach/providers/terms_provider.dart';
import 'package:achawach/routers/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'providers/challenge_provider.dart';
import 'route_observer.dart';
import 'l10n/app_localizations.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Check for authentication token
  final prefs = await SharedPreferences.getInstance();
  final token = prefs.getString('auth_token');

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ChallengeProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
        ChangeNotifierProvider(create: (_) => TermsProvider()),
      ],
      child: MyApp(
          initialRoute:
              token != null ? AppRouter.homeRoute : AppRouter.publicRoute),
    ),
  );
}

class MyApp extends StatelessWidget {
  final String initialRoute;

  const MyApp({super.key, required this.initialRoute});

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return MaterialApp(
          locale: Provider.of<LanguageProvider>(context).locale,
          supportedLocales: AppLocalizations.supportedLocales,
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          localeResolutionCallback: (locale, supportedLocales) {
            if (locale == null) return supportedLocales.first;
            for (var supportedLocale in supportedLocales) {
              if (supportedLocale.languageCode == locale.languageCode) {
                return supportedLocale;
              }
            }
            return supportedLocales.first;
          },
          title: 'Achawach',
          navigatorKey: navigatorKey,
          initialRoute: initialRoute,
          routes: AppRouter.routes,
          onGenerateRoute: AppRouter.generateRoute,
          navigatorObservers: [routeObserver],
          theme: ThemeData(
            primaryColor: AppColors.primary,
            scaffoldBackgroundColor: AppColors.background,
            colorScheme: ColorScheme.fromSeed(
              seedColor: AppColors.primary,
              brightness: Brightness.light,
            ),
            useMaterial3: true,
            fontFamily: 'Poppins', // Use local Poppins font
            appBarTheme: const AppBarTheme(
              backgroundColor: AppColors.background,
              elevation: 0,
              iconTheme: IconThemeData(color: AppColors.text),
              titleTextStyle: TextStyle(
                  color: AppColors.text,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Poppins'),
            ),
            cardTheme: const CardThemeData(
              color: Colors.white,
            ),
            textTheme: Typography.material2018().black.copyWith(
                  bodyLarge: const TextStyle(
                      color: AppColors.text, fontFamily: 'Poppins'),
                  bodyMedium: const TextStyle(
                      color: AppColors.text, fontFamily: 'Poppins'),
                  titleLarge: const TextStyle(
                      color: AppColors.text, fontFamily: 'Poppins'),
                  titleMedium: const TextStyle(
                      color: AppColors.text, fontFamily: 'Poppins'),
                  titleSmall: const TextStyle(
                      color: AppColors.text, fontFamily: 'Poppins'),
                ),
          ),
          darkTheme: ThemeData(
            primaryColor: AppColors.primary,
            scaffoldBackgroundColor: Colors.grey[900],
            colorScheme: ColorScheme.fromSeed(
              seedColor: AppColors.primary,
              brightness: Brightness.dark,
            ),
            useMaterial3: true,
            fontFamily: 'Poppins', // Use local Poppins font
            appBarTheme: AppBarTheme(
              backgroundColor: Colors.grey[900],
              elevation: 0,
              iconTheme: const IconThemeData(color: Colors.white),
              titleTextStyle: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Poppins'),
            ),
            cardTheme: CardThemeData(
              color: Colors.grey[850],
            ),
            textTheme: Typography.material2018().white.apply(
                  fontFamily: 'Poppins',
                ),
          ),
          themeMode: settings.isDarkMode ? ThemeMode.dark : ThemeMode.light,
          onUnknownRoute: (settings) {
            return MaterialPageRoute(
              builder: (context) => Scaffold(
                appBar: AppBar(
                  title: const Text('Unknown Route'),
                ),
                body: const Center(
                  child: Text('Page not found'),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
