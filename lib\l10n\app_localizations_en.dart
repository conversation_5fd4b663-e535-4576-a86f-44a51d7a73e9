// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Achawach';

  @override
  String get welcome => 'Welcome';

  @override
  String get login => 'Login';

  @override
  String get signup => 'Sign Up';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get firstName => 'First Name';

  @override
  String get lastName => 'Last Name';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get home => 'Home';

  @override
  String get categories => 'Categories';

  @override
  String get challenges => 'Challenges';

  @override
  String get profile => 'Profile';

  @override
  String get settings => 'Settings';

  @override
  String get analytics => 'Analytics';

  @override
  String get help => 'Help';

  @override
  String get search => 'Search';

  @override
  String get joinedChallenges => 'Joined Challenges';

  @override
  String get startChallenge => 'Start Challenge';

  @override
  String get joinChallenge => 'Join Challenge';

  @override
  String get score => 'Score';

  @override
  String get level => 'Level';

  @override
  String get lives => 'Lives';

  @override
  String get timeRemaining => 'Time Remaining';

  @override
  String get correct => 'Correct!';

  @override
  String get incorrect => 'Incorrect!';

  @override
  String get gameOver => 'Game Over';

  @override
  String get congratulations => 'Congratulations!';

  @override
  String get levelUp => 'Level Up!';

  @override
  String get noLives => 'No Lives Left';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get backToHome => 'Back to Home';

  @override
  String get soundEffects => 'Sound Effects';

  @override
  String get language => 'Language';

  @override
  String get english => 'English';

  @override
  String get amharic => 'አማርኛ';

  @override
  String get changePassword => 'Change Password';

  @override
  String get logout => 'Logout';

  @override
  String get cancel => 'Cancel';

  @override
  String get save => 'Save';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get loginSuccessful => 'Login Successful!';

  @override
  String get signupSuccessful => 'Sign Up Successful!';

  @override
  String get languageChanged => 'Language changed successfully';

  @override
  String get soundEnabled => 'Sound effects enabled';

  @override
  String get soundDisabled => 'Sound effects disabled';

  @override
  String get aboutApp => 'About Achawach';

  @override
  String get appDescription =>
      'Achawach is a trivia challenge app that tests your knowledge across various categories.';

  @override
  String get version => 'Version';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get agree => 'Agree';

  @override
  String get disagree => 'Disagree';

  @override
  String get welcomeBack => 'Welcome Back';

  @override
  String get signInToAccount => 'Sign in to your account';

  @override
  String get rememberMe => 'Remember me';

  @override
  String get yourFullName => 'Your Full Name';

  @override
  String get pleaseEnterFullName => 'Please enter your full name';

  @override
  String get selectYourInterests => 'Select Your Interests';

  @override
  String get featuredChallenges => 'Featured Challenges';

  @override
  String get challengeCategories => 'Challenge Categories';

  @override
  String get recentActivity => 'Recent Activity';

  @override
  String get noRecentActivity => 'No recent activity';

  @override
  String get achawachChallenges => 'Achawach Challenges';

  @override
  String get close => 'Close';

  @override
  String get choosePreferredLanguage => 'Choose your preferred language';

  @override
  String get enableDisableSoundEffects =>
      'Enable or disable sound effects in the app';

  @override
  String get ensureAccountProtected => 'Ensure your account stays protected';

  @override
  String get pleaseEnterPassword => 'Please enter a password';

  @override
  String get enterYourPassword => 'Enter your password';

  @override
  String get forgotPasswordFeature => 'Forgot password feature coming soon';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get chooseYourProfile => 'Choose Your Profile';

  @override
  String get yourPhoneNumber => 'Your Phone Number';

  @override
  String get createPassword => 'Create Password';

  @override
  String get pleaseSelectAvatar => 'Please select an avatar or upload a photo';

  @override
  String get pleaseSelectCategory =>
      'Please select at least one category of interest';

  @override
  String get pleaseEnterValidPhone => 'Please enter a valid phone number';

  @override
  String get passwordMinLength => 'Password must be at least 6 characters';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get pleaseCheckInformation => 'Please check your information';

  @override
  String get pleaseConfirmPassword => 'Please confirm your password';

  @override
  String get passwordRequirements =>
      'Password must be at least 8 chars,\ninclude upper, lower, number & special char';

  @override
  String get next => 'Next';

  @override
  String get signUp => 'Sign Up';
}
