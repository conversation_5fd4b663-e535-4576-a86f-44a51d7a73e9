import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';

/// Utility class for easy access to localized strings throughout the app
class LocalizationService {
  /// Get the AppLocalizations instance for the given context
  static AppLocalizations of(BuildContext context) {
    return AppLocalizations.of(context)!;
  }

  /// Check if the current locale is RTL (Right-to-Left)
  static bool isRTL(BuildContext context) {
    return Directionality.of(context) == TextDirection.rtl;
  }

  /// Get the current locale
  static Locale getCurrentLocale(BuildContext context) {
    return Localizations.localeOf(context);
  }

  /// Check if the current language is Amharic
  static bool isAmharic(BuildContext context) {
    return getCurrentLocale(context).languageCode == 'am';
  }

  /// Check if the current language is English
  static bool isEnglish(BuildContext context) {
    return getCurrentLocale(context).languageCode == 'en';
  }

  /// Get language-specific font family
  static String getFontFamily(BuildContext context) {
    if (isAmharic(context)) {
      return 'Poppins'; // You can change this to an Amharic-specific font if needed
    }
    return 'Poppins';
  }

  /// Get language-specific text direction
  static TextDirection getTextDirection(BuildContext context) {
    // Amharic is written left-to-right, but you can modify this if needed
    return TextDirection.ltr;
  }

  /// Format numbers according to locale
  static String formatNumber(BuildContext context, num number) {
    // You can implement locale-specific number formatting here
    return number.toString();
  }

  /// Get supported language codes
  static List<String> getSupportedLanguageCodes() {
    return ['en', 'am'];
  }

  /// Get language display name
  static String getLanguageDisplayName(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'am':
        return 'አማርኛ';
      default:
        return 'English';
    }
  }
}

/// Extension to make accessing localizations easier
extension LocalizationExtension on BuildContext {
  AppLocalizations get l10n => LocalizationService.of(this);
}
