import 'dart:async';

import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/core/utils/theme_utils.dart';
import 'package:achawach/models/recent_activity.dart';
import 'package:achawach/screens/categories/category_details.dart';
import 'package:achawach/widgets/animated_bottom_bar.dart';
import 'package:achawach/widgets/category.dart';
import 'package:achawach/widgets/cool_app_bar.dart'; // Import CoolAppBar
import 'package:achawach/widgets/drawer.dart';
import 'package:achawach/widgets/top_challenges_carousel.dart'; // Import the new widget
import 'package:flutter/material.dart';
import 'package:achawach/services/api_service.dart';
import 'package:achawach/models/category.dart';
import 'package:achawach/core/services/localization_service.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey =
      GlobalKey<ScaffoldState>(); // Key for Scaffold
  final ApiService _apiService = ApiService();
  List<Category> _categories = [];
  List<Map<String, dynamic>> _topChallenges = [];
  List<RecentActivity> _recentActivities = [];
  bool _isLoadingCategories = true;
  bool _isLoadingTopChallenges = true;
  bool _isLoadingRecentActivities = true;
  String? _categoriesError;
  String? _topChallengesError;
  String? _recentActivitiesError;
  // late ServiceLocator _serviceLocator;

  // late GameEndService _gameEndService;
// Store subscriptions for cleanup
  // StreamSubscription? _gameEndedSubscription;
  // StreamSubscription? _winnerAnnouncementSubscription;

  @override
  void initState() {
    super.initState();
    _loadCategories();
    _loadTopChallenges();
    _loadRecentActivities();
  }

  Future<void> _loadCategories() async {
    try {
      setState(() {
        _isLoadingCategories = true;
        _categoriesError = null;
      });

      final categories = await _apiService.getCategories();
      setState(() {
        _categories = categories;
        _isLoadingCategories = false;
      });
    } catch (e) {
      _categoriesError = e.toString();
      _isLoadingCategories = false;
    }
  }

  Future<void> _loadTopChallenges() async {
    try {
      setState(() {
        _isLoadingTopChallenges = true;
        _topChallengesError = null;
      });

      final topChallenges = await _apiService.getTopChallenges();
      setState(() {
        _topChallenges = topChallenges;
        _isLoadingTopChallenges = false;
      });
    } catch (e) {
      _topChallengesError = e.toString();
      _isLoadingTopChallenges = false;
    }
  }

  Future<void> _loadRecentActivities() async {
    try {
      setState(() {
        _isLoadingRecentActivities = true;
        _recentActivitiesError = null;
      });

      final recentActivities = await _apiService.getRecentActivity();
      setState(() {
        _recentActivities = recentActivities;
        _isLoadingRecentActivities = false;
      });
    } catch (e) {
      setState(() {
        _recentActivitiesError = e.toString();
        _isLoadingRecentActivities = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey, // Assign the key to Scaffold
      appBar: CoolAppBar(
        title: context.l10n.achawachChallenges,
        leading: IconButton(
          // Drawer Icon as leading
          icon: Icon(Icons.menu, color: ThemeUtils.getTextColor(context)),
          onPressed: () {
            _scaffoldKey.currentState?.openDrawer(); // Open drawer on icon tap
          },
        ),
        // actions: [ ... your actions if any ... ],
      ),
      drawer: const AppDrawer(), // Call function to build the Drawer
      bottomNavigationBar:
          SafeArea(child: const AnimatedBottomBar(currentRoute: '/home')),
      extendBody: true,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.background,
              AppColors.background.withValues(alpha: 0.8),
              AppColors.primary.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: RefreshIndicator(
          onRefresh: () async {
            await Future.wait([
              _loadCategories(),
              _loadTopChallenges(),
              _loadRecentActivities(),
            ]);
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top Challenges Section
                  _buildSectionHeader(
                    title: context.l10n.featuredChallenges,
                    icon: Icons.emoji_events,
                    color: Colors.amber,
                  ),
                  const SizedBox(height: 16),
                  TopChallengesCarousel(
                    topChallenges: _topChallenges,
                    isLoading: _isLoadingTopChallenges,
                    error: _topChallengesError,
                    onRetry: _loadTopChallenges,
                  ),

                  const SizedBox(height: 24),

                  // Categories Section
                  _buildSectionHeader(
                    title: context.l10n.challengeCategories,
                    icon: Icons.category,
                    color: AppColors.primary,
                  ),
                  const SizedBox(height: 16),
                  _buildCategoriesSection(),

                  const SizedBox(height: 24),

                  // Recent Activity Section (placeholder)
                  _buildSectionHeader(
                    title: context.l10n.recentActivity,
                    icon: Icons.history,
                    color: Colors.teal,
                  ),
                  const SizedBox(height: 16),
                  _buildRecentActivitySection(),

                  // Add some bottom padding
                  const SizedBox(height: 80),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader({
    required String title,
    required IconData icon,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: ThemeUtils.getTextColor(context),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoriesSection() {
    if (_isLoadingCategories) {
      return const SizedBox(
        height: 160,
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    } else if (_categoriesError != null) {
      return SizedBox(
        height: 160,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'Error loading categories',
                style: TextStyle(color: Colors.red),
              ),
              const SizedBox(height: 10),
              ElevatedButton(
                onPressed: _loadCategories,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    } else if (_categories.isEmpty) {
      return const SizedBox(
        height: 160,
        child: Center(
          child: Text('No categories available'),
        ),
      );
    } else {
      return SizedBox(
        height: 160,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final category = _categories[index];
            return Padding(
              padding: const EdgeInsets.only(right: 12),
              child: GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => CategoryDetails(
                        selectedChallengeCategories: {
                          'id': category.id,
                          'name': category.name,
                          'icon': category.icon,
                          'color': category.color,
                        },
                      ),
                    ),
                  );
                },
                child: CategoryCard(
                  name: category.name,
                  iconData: category.getIconData(),
                  categoryColor: category.getColor(),
                ),
              ),
            );
          },
        ),
      );
    }
  }

  Widget _buildRecentActivitySection() {
    if (_isLoadingRecentActivities) {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          color: ThemeUtils.getCardColor(context),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    } else if (_recentActivitiesError != null) {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          color: ThemeUtils.getCardColor(context),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 40,
                color: Colors.red,
              ),
              const SizedBox(height: 8),
              Text(
                'Error loading activities',
                style: GoogleFonts.poppins(
                  color: Colors.red,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 10),
              ElevatedButton(
                onPressed: _loadRecentActivities,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    } else if (_recentActivities.isEmpty) {
      return Container(
        height: 120,
        decoration: BoxDecoration(
          color: ThemeUtils.getCardColor(context),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.history_outlined,
                size: 40,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 8),
              Text(
                context.l10n.noRecentActivity,
                style: GoogleFonts.poppins(
                  color: ThemeUtils.getSecondaryTextColor(context),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      return Container(
        decoration: BoxDecoration(
          color: ThemeUtils.getCardColor(context),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount:
              _recentActivities.length > 5 ? 5 : _recentActivities.length,
          itemBuilder: (context, index) {
            final activity = _recentActivities[index];
            return _buildActivityItem(activity);
          },
        ),
      );
    }
  }

  Widget _buildActivityItem(RecentActivity activity) {
    // Format the date
    final dateFormat = DateFormat('MMM d, yyyy');
    final timeFormat = DateFormat('h:mm a');
    final formattedDate = dateFormat.format(activity.createdAt);
    final formattedTime = timeFormat.format(activity.createdAt);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.teal.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.history,
              color: Colors.teal,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.activity,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: ThemeUtils.getTextColor(context),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '$formattedDate at $formattedTime',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: ThemeUtils.getSecondaryTextColor(context),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
