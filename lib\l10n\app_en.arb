{"@@locale": "en", "appTitle": "<PERSON><PERSON><PERSON><PERSON>", "@appTitle": {"description": "The title of the application"}, "welcome": "Welcome", "@welcome": {"description": "Welcome message"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "signup": "Sign Up", "@signup": {"description": "Sign up button text"}, "email": "Email", "@email": {"description": "Email field label"}, "password": "Password", "@password": {"description": "Password field label"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Confirm password field label"}, "phoneNumber": "Phone Number", "@phoneNumber": {"description": "Phone number field label"}, "firstName": "First Name", "@firstName": {"description": "First name field label"}, "lastName": "Last Name", "@lastName": {"description": "Last name field label"}, "forgotPassword": "Forgot Password?", "@forgotPassword": {"description": "Forgot password link text"}, "dontHaveAccount": "Don't have an account?", "@dontHaveAccount": {"description": "Text asking if user doesn't have an account"}, "alreadyHaveAccount": "Already have an account?", "@alreadyHaveAccount": {"description": "Text asking if user already has an account"}, "home": "Home", "@home": {"description": "Home screen title"}, "categories": "Categories", "@categories": {"description": "Categories section title"}, "challenges": "Challenges", "@challenges": {"description": "Challenges section title"}, "profile": "Profile", "@profile": {"description": "Profile screen title"}, "settings": "Settings", "@settings": {"description": "Settings screen title"}, "analytics": "Analytics", "@analytics": {"description": "Analytics screen title"}, "help": "Help", "@help": {"description": "Help screen title"}, "search": "Search", "@search": {"description": "Search screen title"}, "joinedChallenges": "Joined Challenges", "@joinedChallenges": {"description": "Joined challenges screen title"}, "startChallenge": "Start Challenge", "@startChallenge": {"description": "Start challenge button text"}, "joinChallenge": "Join Challenge", "@joinChallenge": {"description": "Join challenge button text"}, "score": "Score", "@score": {"description": "Score label"}, "level": "Level", "@level": {"description": "Level label"}, "lives": "Lives", "@lives": {"description": "Lives label"}, "timeRemaining": "Time Remaining", "@timeRemaining": {"description": "Time remaining label"}, "correct": "Correct!", "@correct": {"description": "Correct answer feedback"}, "incorrect": "Incorrect!", "@incorrect": {"description": "Incorrect answer feedback"}, "gameOver": "Game Over", "@gameOver": {"description": "Game over message"}, "congratulations": "Congratulations!", "@congratulations": {"description": "Congratulations message"}, "levelUp": "Level Up!", "@levelUp": {"description": "Level up message"}, "noLives": "No Lives Left", "@noLives": {"description": "No lives left message"}, "tryAgain": "Try Again", "@tryAgain": {"description": "Try again button text"}, "backToHome": "Back to Home", "@backToHome": {"description": "Back to home button text"}, "soundEffects": "Sound Effects", "@soundEffects": {"description": "Sound effects setting label"}, "language": "Language", "@language": {"description": "Language setting label"}, "english": "English", "@english": {"description": "English language option"}, "amharic": "አማርኛ", "@amharic": {"description": "Amharic language option"}, "changePassword": "Change Password", "@changePassword": {"description": "Change password option"}, "logout": "Logout", "@logout": {"description": "Logout button text"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "save": "Save", "@save": {"description": "Save button text"}, "loading": "Loading...", "@loading": {"description": "Loading message"}, "error": "Error", "@error": {"description": "Error message"}, "success": "Success", "@success": {"description": "Success message"}, "loginSuccessful": "Login Successful!", "@loginSuccessful": {"description": "Login successful message"}, "signupSuccessful": "Sign Up Successful!", "@signupSuccessful": {"description": "Sign up successful message"}, "languageChanged": "Language changed successfully", "@languageChanged": {"description": "Language changed confirmation message"}, "soundEnabled": "Sound effects enabled", "@soundEnabled": {"description": "Sound enabled confirmation message"}, "soundDisabled": "Sound effects disabled", "@soundDisabled": {"description": "Sound disabled confirmation message"}, "aboutApp": "About Achawach", "@aboutApp": {"description": "About app dialog title"}, "appDescription": "Achawach is a trivia challenge app that tests your knowledge across various categories.", "@appDescription": {"description": "App description text"}, "version": "Version", "@version": {"description": "Version label"}, "termsAndConditions": "Terms and Conditions", "@termsAndConditions": {"description": "Terms and conditions title"}, "agree": "Agree", "@agree": {"description": "Agree button text"}, "disagree": "Disagree", "@disagree": {"description": "Disagree button text"}, "welcomeBack": "Welcome Back", "@welcomeBack": {"description": "Welcome back message on login screen"}, "signInToAccount": "Sign in to your account", "@signInToAccount": {"description": "Sign in instruction text"}, "rememberMe": "Remember me", "@rememberMe": {"description": "Remember me checkbox text"}, "yourFullName": "Your Full Name", "@yourFullName": {"description": "Full name field placeholder"}, "pleaseEnterFullName": "Please enter your full name", "@pleaseEnterFullName": {"description": "Full name validation message"}, "selectYourInterests": "Select Your Interests", "@selectYourInterests": {"description": "Interest selection header"}, "featuredChallenges": "Featured Challenges", "@featuredChallenges": {"description": "Featured challenges section title"}, "challengeCategories": "Challenge Categories", "@challengeCategories": {"description": "Challenge categories section title"}, "recentActivity": "Recent Activity", "@recentActivity": {"description": "Recent activity section title"}, "noRecentActivity": "No recent activity", "@noRecentActivity": {"description": "No recent activity placeholder text"}, "achawachChallenges": "Achawach Challenges", "@achawachChallenges": {"description": "App bar title for home screen"}, "close": "Close", "@close": {"description": "Close button text"}, "choosePreferredLanguage": "Choose your preferred language", "@choosePreferredLanguage": {"description": "Language selection subtitle"}, "enableDisableSoundEffects": "Enable or disable sound effects in the app", "@enableDisableSoundEffects": {"description": "Sound effects setting subtitle"}, "ensureAccountProtected": "Ensure your account stays protected", "@ensureAccountProtected": {"description": "Change password subtitle"}, "pleaseEnterPassword": "Please enter a password", "@pleaseEnterPassword": {"description": "Password validation message"}, "enterYourPassword": "Enter your password", "@enterYourPassword": {"description": "Password field hint text"}, "forgotPasswordFeature": "Forgot password feature coming soon", "@forgotPasswordFeature": {"description": "Forgot password feature message"}, "dashboard": "Dashboard", "@dashboard": {"description": "Dashboard menu item"}, "helpSupport": "Help & Support", "@helpSupport": {"description": "Help and support menu item"}, "chooseYourProfile": "Choose Your Profile", "@chooseYourProfile": {"description": "Profile selection title"}, "yourPhoneNumber": "Your Phone Number", "@yourPhoneNumber": {"description": "Phone number input title"}, "createPassword": "Create Password", "@createPassword": {"description": "Password creation title"}, "pleaseSelectAvatar": "Please select an avatar or upload a photo", "@pleaseSelectAvatar": {"description": "Avatar selection validation message"}, "pleaseSelectCategory": "Please select at least one category of interest", "@pleaseSelectCategory": {"description": "Category selection validation message"}, "pleaseEnterValidPhone": "Please enter a valid phone number", "@pleaseEnterValidPhone": {"description": "Phone validation message"}, "passwordMinLength": "Password must be at least 6 characters", "@passwordMinLength": {"description": "Password minimum length validation"}, "passwordsDoNotMatch": "Passwords do not match", "@passwordsDoNotMatch": {"description": "Password confirmation validation"}, "pleaseCheckInformation": "Please check your information", "@pleaseCheckInformation": {"description": "General validation message"}, "pleaseConfirmPassword": "Please confirm your password", "@pleaseConfirmPassword": {"description": "Confirm password validation"}, "passwordRequirements": "Password must be at least 8 chars,\ninclude upper, lower, number & special char", "@passwordRequirements": {"description": "Strong password requirements"}, "next": "Next", "@next": {"description": "Next button text"}, "signUp": "Sign Up", "@signUp": {"description": "Sign up button text"}}